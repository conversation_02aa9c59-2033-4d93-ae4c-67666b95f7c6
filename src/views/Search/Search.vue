<template>
  <div>
    <!-- 顶部搜索栏 -->
    <van-sticky>
      <SearchHeader ref="searchHeaderRef" />
    </van-sticky>

    <main class="mt-[18px] px-[16px]">
      <!-- 搜索历史 -->
      <div>
        <div class="flex items-center justify-between">
          <span
            class="sf-pro-display-semibold font-bold text-[18px] text-custom-color-2"
          >Search history</span>

          <SvgIcon name="search-delete" class="text-[20px]" />
        </div>

        <!-- 搜索历史列表 -->
        <div class="flex items-center flex-wrap mt-[13px]">
          <div
            v-for="item in 10"
            :key="item"
            class="w-fit h-fit py-[15px] px-[20px] bg-custom-color-13 rounded-[100px] sf-pro-display-medium text-[16px] text-custom-color-12 mr-[10px] mb-[13px]"
            @click="handleSearch('X830')"
          >X830</div>
        </div>
      </div>

      <!-- 猜你喜欢 -->
      <div v-if="mayLikeTextList.length > 0">
        <div class="flex items-center justify-between mt-[27px]">
          <span
            class="sf-pro-display-semibold font-bold text-[18px] text-custom-color-2"
          >You may like</span>
        </div>
        <!-- 列表 -->
        <div class="mt-[13px] px-[2px] grid grid-cols-2 gap-y-[15px] gap-x-[57px]">
          <span
            v-for="item in mayLikeTextList"
            :key="item.id"
            class="sf-pro-display-medium text-[16px] text-custom-color-12 van-ellipsis"
            @click="handleToProductDetail(item.id)"
          >{{ item.name }}</span>
        </div>
      </div>

      <!-- 热搜 -->
      <div v-if="hotSearchProducts">
        <div class="flex items-center justify-between mt-[40px]">
          <span
            class="sf-pro-display-semibold font-bold text-[18px] text-custom-color-2"
          >Hot search</span>
        </div>

        <!-- 图片 -->
        <div class="mt-[16px] grid grid-cols-3 gap-x-[20px]">
          <div
            v-for="(item, index) in hotSearchProducts?.pic_products"
            :key="item.id"
            class="w-[120px] h-[120px] relative"
            @click="handleToProductDetail(item.id)">
            <img
              :src="hotImage(index + 1)"
              class="w-[25px] h-[33px] absolute top-[-3px] left-[7px]"
              alt=""
            />
            <img
              :src="item.image"
              class="w-[120px] h-[120px]"
              alt=""
            />
          </div>
        </div>

        <!-- 文字 -->
        <div class="mt-[12px]">
          <div
            v-for="(item, index) in hotSearchProducts?.normal_products"
            :key="item.id"
            class="flex items-center mb-[14px]">
            <div class="w-[21px] h-[21px] flex items-center justify-center mr-[21px]">
              <SvgIcon
                :name="`search-hot-${index + 4}`"
                :width="index + 4 <= 9 ? '13' : '18'"
                :height="index + 4 <= 9 ? '13' : '18'"
                class="translate-y-[-1px]"
              ></SvgIcon>
            </div>
            <span
              class="sf-pro-display-medium text-[16px] leading-[16px] text-custom-color-12 van-ellipsis"
              @click="handleToProductDetail(item.id)"
            >{{ item.name }}</span>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { showToast } from 'vant'
import { useShopStore } from '@/stores/shop'
import { searchService } from '@/api/search'
import type { MayLikeTextListDataProductsItem, HotSearchProductsData } from '@/types/search'
import SearchHeader from '@/components/Common/SearchHeader.vue'
import router from '@/router'

const shopStore = useShopStore()
const mayLikeTextList = ref<MayLikeTextListDataProductsItem[]>([])
const hotSearchProducts = ref<HotSearchProductsData>()
const searchHeaderRef = ref<InstanceType<typeof SearchHeader>>()

const hotImage = (index: number) => {
  return new URL(`../../assets/images/search/hot-${index}.png`, import.meta.url).href;
}

const fetchMayLikeTextList = async () => {
  try {
    const res = await searchService.getMayLikeTextList({
      shop_id: shopStore.shopAddress!.shopId.toString()
    })
    console.log('[fetchMayLikeTextList]res:', res)
    if (res.status === 1) {
      mayLikeTextList.value = res.data.products
    } else {
      showToast(res.error || '获取猜你喜欢列表失败')
    }
  } catch (error) {
    console.error(error)
    showToast((error as string) || '获取猜你喜欢列表失败')
  }
}

const fetchHotSearchProducts = async () => {
  try {
    const res = await searchService.getHotSearchProducts({
      shop_id: shopStore.shopAddress!.shopId.toString()
    })
    console.log('[fetchHotSearchProducts]res:', res)
    if (res.status === 1) {
      hotSearchProducts.value = res.data
    } else {
      showToast(res.error || '获取热搜列表失败')
    }
  } catch (error) {
    console.error(error)
    showToast((error as string) || '获取热搜列表失败')
  }
}

fetchMayLikeTextList()
fetchHotSearchProducts()


const handleSearch = (keyword: string) => {
  searchHeaderRef.value?.handleSearch(keyword)
}

const handleToProductDetail = (id: number) => {
  // console.log('[handleToProductDetail]id:', id)
  router.push(`/product-detail/${id}`)
}
</script>
