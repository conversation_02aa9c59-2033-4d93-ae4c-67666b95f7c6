<script setup lang="ts">
import { RouterView } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useCartStore } from '@/stores/cart'
import { useShopStore } from '@/stores/shop'
import { usePageStackStore } from '@/stores/pageStack'
import i18n from '@/locales'

// 使用页面栈store的动态include列表
const pageStackStore = usePageStackStore()
const includeList = pageStackStore.keepAliveInclude

// 图片预加载
const images = [
  '/images/pull-down-to-refresh.gif',
  new URL('@/assets/images/order/delivery-bg.png', import.meta.url).href,
  new URL('@/assets/images/order/pickup-bg.png', import.meta.url).href
]
const preloadImage = () => {
  images.forEach((image) => {
    const img = new Image()
    img.src = image
  })
}
preloadImage()

const getCartList = async () => {
  const userStore = useUserStore()
  const cartStore = useCartStore()
  const shopStore = useShopStore()
  if (shopStore.shopAddress && shopStore.shopAddress.shopId !== 0) {
    if (userStore.isLogin) {
      cartStore.getCartList({ showLoading: false })
    } else {
      console.log("[App.vue][getCartList]cartStore.guestProducts:", cartStore.guestProducts)
      await cartStore.getCartList({
        params: {
          shop_id: shopStore.shopAddress.shopId,
          force_refresh: 1,
          guest_products: JSON.stringify(cartStore.guestProducts)
        },
        showLoading: false
      })
      cartStore.guestProductsStorage.used = true
    }
  }
}
getCartList()

console.log('当前环境：', localStorage.getItem('apiEnv'), import.meta.env.VITE_USER_NODE_ENV, i18n.global.locale.value)
</script>

<template>
  <RouterView v-slot="{ Component }">
    <keep-alive :include="includeList">
      <component :is="Component" />
    </keep-alive>
  </RouterView>
</template>

<style lang="scss" scoped>
:deep(.van-image-preview) {
  background-color: transparent !important;
}

:deep(.search-input) {
  --van-cell-vertical-padding: 0;
  --van-cell-horizontal-padding: 11px;
  --van-field-placeholder-text-color: #5e5e5e;
  height: 40px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border-radius: 50px;

  .van-field__left-icon {
    margin-right: 9px;
  }

  .van-field__label {
    font-size: 15px;
    color: $input-placeholder-color;
    font-family: 'SF Compact Text Medium';
  }
}

/*:deep(.van-popup) {
  border-radius: 20px 20px 0 0 !important;
}*/

:deep(.referralLink) {
  border-radius: 0 !important;
}

:deep(.btn-bg-1) {
  background: rgb(250, 63, 37);
  background: linear-gradient(0deg, rgba(250, 63, 37, 1) 0%, rgba(251, 110, 49, 1) 100%);
}

:deep(.bottom-bar) {
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(255, 255, 255, 0.75);
}

:deep(.combo-save-bg) {
  background: rgb(251, 107, 48);
  background: linear-gradient(0deg, rgba(251, 107, 48, 1) 0%, rgba(250, 63, 38, 1) 100%);
}

:deep(.combo-save-membership-bg) {
  background: linear-gradient(180deg, #FFE5B7 0%, #FFF1CD 100%);
}

:deep(.combo-detail) {
  --van-action-sheet-max-height: 90% !important;
}

:deep(.input-1) {
  padding: 0 16px 0 15px;
  width: 100%;
  height: 64px;
  display: flex;
  align-items: center;
  border-radius: 100px;
  border: 1px solid $input-bg-color;
  --van-field-placeholder-text-color: #757575;

  .van-field__left-icon,
  .van-cell__value {
    width: fit-content;
    height: fit-content;
  }

  .van-field__left-icon {
    margin-right: 16px;
  }

  .van-field__control {
    color: $input-text-color;
    font-size: 18px;
    line-height: 18px;
    font-family: 'SF Compact Text Medium';
  }
}

:deep(.membership-checkbox) {
  .van-checkbox__icon {
    font-size: 18rem;
    .van-badge__wrapper {
      border-radius: 3.27rem;
    }
  }
  .van-checkbox__icon--checked {
    .van-badge__wrapper{
      border-color: #000000;
      background-color: #000000;
    }
    .van-icon-success{
      color: #F6CF7F;
    }
  }
}

:deep(.scrollbar-hidden) {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */

  &::-webkit-scrollbar {
    display: none;
  }
}

:deep(.text-two-lines-ellipsis) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

:deep(.gradient-text) {
  -webkit-background-clip: text !important;
  color: transparent;
}
</style>
