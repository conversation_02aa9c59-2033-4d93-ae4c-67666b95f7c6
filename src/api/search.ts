import { get, post } from '../utils'
import type {
    RestaurantTypeProductListParams,
    RestaurantTypeProductListData,
    ChildCategoriesParams,
    ShopMenusParams,
    MayLikeTextListData,
    HotSearchProductsData
} from '@/types/search'

const searchService = {
    // 7.2 餐馆类型关联的商品
    getRestaurantTypeProductList: (params: RestaurantTypeProductListParams) => get<Response<RestaurantTypeProductListData>>('api/shop/restaurant_type_product_list', params),
    // 11.2 二级分类（带商品）
    getChildCategories: (params: ChildCategoriesParams) => get<Response<RestaurantTypeProductListData>>('api/shop/child_categories', params),
    // 11.3 商品列表(包含分类信息，卡片形式)注：第一次调用
    getShopMenus: (params: ShopMenusParams) => get<Response<any>>('api/shop/menus', params),
    // 13.1 可能喜欢（may）(文字形式)
    getMayLikeTextList: (params: any) => get<Response<MayLikeTextListData>>('api/like/may', params),
    // 12.3 热门搜索产品
    getHotSearchProducts: (params: any) => get<Response<HotSearchProductsData>>('api/search/hot_products', params)
}

export { searchService }
