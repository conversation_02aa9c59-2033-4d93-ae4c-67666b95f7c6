<template>
  <div class="pt-[9px] pb-[8px] bg-primary">
    <!-- 店铺和消息 -->
    <div class="flex items-center justify-between">
      <!-- left -->
      <div class="py-[4px] pl-[20px] flex items-center" @click="handleChooseAddress">
        <SvgIcon name="home-shop-icon" color="#fff" class="text-[22px] mr-[5px]"></SvgIcon>
        <span class="sf-pro-display-semibold text-white text-[15px] max-w-[240px] truncate">{{
          shopAddress?.name
        }}</span>
        <SvgIcon name="home-shop-down-icon" class="text-[14px] h-[8px] ml-[5px]"></SvgIcon>
      </div>
      <!-- right -->
      <div class="pr-[12px]">
        <div class="w-[30px] h-[30px] relative">
          <div
            class="w-[10px] h-[10px] bg-red-500 absolute top-[1px] right-[1px] rounded-full"
          ></div>
          <SvgIcon name="home-message-icon" color="#fff" class="text-white text-[30px]"></SvgIcon>
        </div>
      </div>
    </div>
    <!-- 搜索 -->
    <div class="px-[12px] mt-[9px] relative">
      <div class="absolute top-0 left-0 w-full h-full z-10" @click="search"></div>
      <van-field class="search-input" :placeholder="searchPlaceholder">
        <template #left-icon>
          <SvgIcon name="home-search-icon" class="text-[22px]"></SvgIcon>
        </template>
      </van-field>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import type { Shop } from '@/types/home'
import { useShopStore } from '@/stores/shop'

const shopStore = useShopStore()
const router = useRouter()

defineProps<{
  shop: Shop
}>()

const shopAddress = computed(() => shopStore.shopAddress)
const searchPlaceholder = computed(() => {
  let placeholder = ''
  if (['development', 'test'].includes(import.meta.env.VITE_USER_NODE_ENV)) {
    placeholder = localStorage.getItem('apiEnv') ?? 'dev1'
    placeholder += '--' + shopStore.shopAddress?.shopId.toString()
  }
  return placeholder
})

const search = () => {
  router.push('/search')
}

const handleChooseAddress = () => {
  router.push('/choose-address')
}
</script>
