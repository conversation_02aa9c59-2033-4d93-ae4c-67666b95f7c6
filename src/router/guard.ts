import router from './index'
import type {
  NavigationGuardNext,
  RouteLocationNormalized,
  RouteLocationNormalizedLoaded
} from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useShopStore } from '@/stores/shop'
import { usePageStackStore } from '@/stores/pageStack'

// 允许店铺ID不存在的页面
const allowNoShopIdPages = ['/choose-address', '/warehouse-address', '/receive-invitation']

// 判断是否为后退导航
const isBackNavigation = (to: RouteLocationNormalized, from: RouteLocationNormalizedLoaded): boolean => {
  // 通过history state判断导航方向
  if (typeof window !== 'undefined' && window.history.state) {
    const state = window.history.state
    // Vue Router会在state中记录position，后退时position会减小
    if (state.back && state.current && state.forward) {
      return state.position < state.current
    }
  }

  // 备用判断：检查是否是常见的后退路径模式
  const backPatterns = [
    { from: '/product-detail', to: '/' },
    { from: '/search-result', to: '/search' },
    { from: '/cart', to: '/' },
    // 可以根据实际业务添加更多模式
  ]

  return backPatterns.some(pattern =>
    from.path.startsWith(pattern.from) && to.path === pattern.to
  )
}

router.beforeEach(
  async (
    to: RouteLocationNormalized,
    from: RouteLocationNormalizedLoaded,
    next: NavigationGuardNext
  ) => {
    const shopStore = useShopStore()
    if (
      (!shopStore.shopAddress || shopStore.shopAddress.shopId === 0) &&
      !allowNoShopIdPages.includes(to.path)
    ) {
      next('/choose-address')
    }

    const userStore = useUserStore()
    if (userStore.isLogin && !userStore.userInfo) {
      await userStore.getUserDetail()
    }

    // 页面栈管理逻辑
    const pageStackStore = usePageStackStore()

    // 判断导航类型并更新页面栈
    if (from.name && to.name) {
      const isBack = isBackNavigation(to, from)

      if (isBack) {
        // 后退导航：从栈中移除当前页面
        pageStackStore.popPage(from.name as string)
        console.log('[Router Guard] 后退导航，移除页面:', from.name)
      } else {
        // 前进导航：将当前页面添加到栈中
        pageStackStore.pushPage(from.name as string, from.path)
        console.log('[Router Guard] 前进导航，页面入栈:', from.name)
      }
    }

    next()
  }
)
