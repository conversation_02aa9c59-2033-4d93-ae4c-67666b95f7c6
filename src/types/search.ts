import type { ProductsItem } from './home'

// 餐馆类型关联的商品接口传参
export interface RestaurantTypeProductListParams {
    shop_id: number | string
    restaurant_type_id: number
    keyword: string
    restaurant_type: string
}

// 餐馆类型关联的商品接口返回数据
export interface RestaurantTypeProductListData {
    category_list: RestaurantTypeProductListCategoryListItem[]
    product_list: RestaurantTypeProductListProductListItem[]
}

export interface RestaurantTypeProductListCategoryListItem {
    id: number
    name: string
    image: string
    memo: string
    type: number
    event_url: string
    count: number
}

export interface RestaurantTypeProductListProductListItem {
    category: RestaurantTypeProductListCategoryListItem
    products: ProductsItem[]
}
/*
export interface RestaurantTypeProductListProductListItemProductsItem {
    event: string
    data: RestaurantTypeProductListProductListItemProductsItemData
}

export interface RestaurantTypeProductListProductListItemProductsItemData {
    price: number
    price_show: string
    unit_price_show: string
    minimum_price: number
    minimum_price_show: string
    unit_minimum_price_show: string
    price_after_discount: number
    price_after_discount_show: string
    unit_price_after_discount: number
    unit_price_after_discount_show: string
    org_price: number
    org_price_show: string
    unit_org_price_show: string
    image: string
    stock: number
    weight_unit: string
    need_weight: number
    weight_value: string
    unit_price: number
    unit_org_price: number
    label: string
    favorite: number
    max: number
    min: number
    stock_status: number
    stock_status_desc: string
    activity_desc: string[]
    activity_tag: string[]
    title_activity_icon: string
    image_activity_icon: string
    hide_cart: number
}
*/

// 二级分类（带商品）接口传参
export interface ChildCategoriesParams {
    shop_id: number | string
    top_category_id: number
    keyword: string
    category_word: string
    product_ids: string
}

// 商品列表(包含分类信息，卡片形式)接口传参
export interface ShopMenusParams {
    shop_id: number | string
    page: number
    category_id: number
    restaurant_type_id?: number
}

// 商品列表(包含分类信息，卡片形式)接口返回数据
export interface ShopMenusData {
    category: ShopMenusCategory
    products: ProductsItem[]
    page: ShopMenusPage
}

export interface ShopMenusCategory {
    id: number
    name: string
    image: string
    memo: string
    type: number
    event_url: string
    count: number
}

export interface ShopMenusPage {
    current_page: number
    per_page: number
    last_page: number
    total: number
}

// 可能喜欢（may）(文字形式)接口返回格式
export interface MayLikeTextListData {
    products: MayLikeTextListDataProductsItem[]
}

export interface MayLikeTextListDataProductsItem {
    id: number
    name: string
}

// 热门搜索产品接口返回格式
export interface HotSearchProductsData {
    pic_products: HotSearchProductsDataPicProductsItem[]
    normal_products: HotSearchProductsDataNormalProductsItem[]
}

export interface HotSearchProductsDataPicProductsItem {
    id: number
    name: string
    image: string
}

export interface HotSearchProductsDataNormalProductsItem {
    id: number
    name: string
    image: string
}
