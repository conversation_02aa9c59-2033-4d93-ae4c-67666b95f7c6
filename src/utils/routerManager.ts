import { Router } from 'vue-router'
import type { RouteLocationRaw, RouteLocationNormalized } from 'vue-router'
import router from '@/router'

interface PageStackItem {
  path: string;
  name: string | null | undefined;
  timestamp: number;
  meta: Record<string, any>;
}

/**
 * 页面实例栈管理类
 * 实现页面导航过程中页面实例的保存和恢复
 */
class RouterManager {
  private pageStack: PageStackItem[] = []
  private originalPush: Router['push']
  private originalBack: Router['back']
  private originalGo: Router['go']
  private router: Router
  private isPoppingState: boolean = false

  constructor(router: Router) {
    this.router = router
    // 保存原始方法引用
    this.originalPush = router.push
    this.originalBack = router.back
    this.originalGo = router.go

    // 重写路由方法
    this.overrideRouterMethods()
    
    // 监听路由变化
    this.setupRouteChangeListener()
  }

  /**
   * 设置路由变化监听器
   */
  private setupRouteChangeListener(): void {
    this.router.afterEach((to, from) => {
      // 如果是由于我们自己的操作引起的返回，不需要特殊处理
      if (this.isPoppingState) {
        this.isPoppingState = false
        return
      }
      
      // 记录当前路由信息
      this.recordRouteInfo(to, from)
    })
  }

  /**
   * 记录路由信息
   */
  private recordRouteInfo(to: RouteLocationNormalized, from: RouteLocationNormalized): void {
    console.log(`[RouterManager] Route changed: ${from.path} -> ${to.path}`)
  }

  /**
   * 查找路由在栈中的位置
   */
  private findRouteInStack(path: string): number {
    return this.pageStack.findIndex(item => item.path === path)
  }

  /**
   * 重写路由的导航方法
   */
  private overrideRouterMethods(): void {
    // 重写 push 方法，实现跳转页面前保留当前页实例
    this.router.push = (to: RouteLocationRaw) => {
      const currentRoute = this.router.currentRoute.value
      
      // 将当前页面信息添加到页面栈中
      if (currentRoute) {
        const existingIndex = this.findRouteInStack(currentRoute.path)
        
        if (existingIndex === -1) {
          // 如果栈中不存在当前页面，则添加
          this.pageStack.push({
            path: currentRoute.path,
            name: currentRoute.name as string | null | undefined,
            timestamp: Date.now(),
            meta: { ...currentRoute.meta }
          })
        }
      }
      
      console.log('[RouterManager] Push to:', typeof to === 'string' ? to : JSON.stringify(to), 'Stack:', this.pageStack.map(item => item.path))
      
      // 调用原始的 push 方法
      return this.originalPush.call(this.router, to)
    }

    // 重写 back 方法，返回前从页面栈中删除当前页面实例
    this.router.back = () => {
      this.isPoppingState = true
      this.removeCurrentPageFromStack()
      console.log('[RouterManager] Back, Stack:', this.pageStack.map(item => item.path))
      
      // 调用原始的 back 方法
      return this.originalBack.call(this.router)
    }

    // 重写 go 方法，go(-1) 与 back 行为一致
    this.router.go = (delta: number) => {
      if (delta === -1) {
        // 如果是返回上一页，与 back() 行为一致
        this.isPoppingState = true
        this.removeCurrentPageFromStack()
        console.log('[RouterManager] Go(-1), Stack:', this.pageStack.map(item => item.path))
      } else if (delta < -1) {
        // 如果回退多步，则需要移除对应数量的页面
        this.isPoppingState = true
        for (let i = 0; i > delta; i--) {
          this.removeCurrentPageFromStack()
        }
        console.log(`[RouterManager] Go(${delta}), Stack:`, this.pageStack.map(item => item.path))
      }
      
      // 调用原始的 go 方法
      return this.originalGo.call(this.router, delta)
    }
  }

  /**
   * 从页面栈中移除当前页面
   */
  private removeCurrentPageFromStack(): void {
    const currentRoute = this.router.currentRoute.value
    const index = this.findRouteInStack(currentRoute.path)
    
    if (index !== -1) {
      this.pageStack.splice(index, 1)
    }
  }

  /**
   * 清空页面栈
   */
  public clearPageStack(): void {
    this.pageStack = []
  }

  /**
   * 获取当前页面栈
   */
  public getPageStack(): PageStackItem[] {
    return [...this.pageStack]
  }
}

// 创建路由管理实例
const routerManager = new RouterManager(router)

export default routerManager
