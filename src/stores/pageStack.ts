import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export interface PageStackItem {
  name: string
  path: string
  timestamp: number
}

export const usePageStackStore = defineStore('pageStack', () => {
  // 页面栈，存储需要保持状态的页面
  const pageStack = ref<PageStackItem[]>([])
  
  // 固定需要缓存的页面（原有的includeList）
  const fixedCachePages = ref<string[]>(['Home', 'Browse', 'Cart', 'WarehouseAddress', 'MembershipActivate'])
  
  // 页面栈最大长度，防止内存泄漏
  const maxStackSize = 20
  
  // 动态计算keep-alive的include列表
  const keepAliveInclude = computed(() => {
    const stackPageNames = pageStack.value.map(item => item.name)
    // 合并固定缓存页面和页面栈中的页面，去重
    return [...new Set([...fixedCachePages.value, ...stackPageNames])]
  })
  
  // 添加页面到栈中
  const pushPage = (name: string, path: string) => {
    if (!name) return
    
    // 检查是否已存在，避免重复添加
    const existingIndex = pageStack.value.findIndex(item => item.name === name)
    if (existingIndex !== -1) {
      // 如果已存在，更新时间戳并移到栈顶
      const existingItem = pageStack.value.splice(existingIndex, 1)[0]
      existingItem.timestamp = Date.now()
      pageStack.value.push(existingItem)
      return
    }
    
    // 添加新页面到栈顶
    pageStack.value.push({
      name,
      path,
      timestamp: Date.now()
    })
    
    // 限制栈大小
    if (pageStack.value.length > maxStackSize) {
      pageStack.value.shift() // 移除最旧的页面
    }
    
    console.log('[PageStack] 页面入栈:', name, '当前栈:', pageStack.value.map(p => p.name))
  }
  
  // 从栈中移除页面
  const popPage = (name?: string) => {
    if (!name) {
      // 如果没有指定页面名，移除栈顶页面
      const removed = pageStack.value.pop()
      console.log('[PageStack] 移除栈顶页面:', removed?.name, '当前栈:', pageStack.value.map(p => p.name))
      return removed
    }
    
    // 移除指定页面
    const index = pageStack.value.findIndex(item => item.name === name)
    if (index !== -1) {
      const removed = pageStack.value.splice(index, 1)[0]
      console.log('[PageStack] 移除指定页面:', name, '当前栈:', pageStack.value.map(p => p.name))
      return removed
    }
    
    return null
  }
  
  // 检查页面是否在栈中
  const isPageInStack = (name: string): boolean => {
    return pageStack.value.some(item => item.name === name)
  }
  
  // 获取栈顶页面
  const getTopPage = (): PageStackItem | null => {
    return pageStack.value.length > 0 ? pageStack.value[pageStack.value.length - 1] : null
  }
  
  // 清空页面栈
  const clearStack = () => {
    pageStack.value = []
    console.log('[PageStack] 清空页面栈')
  }
  
  // 获取页面栈信息（用于调试）
  const getStackInfo = () => {
    return {
      stack: pageStack.value,
      keepAliveInclude: keepAliveInclude.value,
      stackSize: pageStack.value.length
    }
  }
  
  return {
    pageStack,
    fixedCachePages,
    keepAliveInclude,
    pushPage,
    popPage,
    isPageInStack,
    getTopPage,
    clearStack,
    getStackInfo
  }
})
